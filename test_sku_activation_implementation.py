#!/usr/bin/env python3
"""
Test script for the _create_sku_activation_records method implementation.

This script tests the newly implemented method to ensure it:
1. Correctly retrieves created SKU codes from the onboarding entity
2. Gets service parameters from the Param table
3. Creates SkuActivation records for each SKU and service combination
4. Returns proper activation record identifiers
5. <PERSON><PERSON> error cases gracefully
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sku_activation_records_implementation():
    """Test the _create_sku_activation_records method implementation"""
    
    print("🧪 Testing _create_sku_activation_records Implementation")
    print("=" * 60)
    
    try:
        # Import required classes
        from cataloging_service.domain.entities.properties.property_onboarding import PropertyOnboardingEntity
        from cataloging_service.domain.services.template.property_onboarding_service import PropertyOnboardingService
        from cataloging_service.models import Param, SkuActivation, PropertySku, Sku
        
        print("✅ Successfully imported required classes")
        
        # Test 1: Create mock onboarding entity with SKU codes
        print("\n📋 Test 1: Creating mock onboarding entity")
        onboarding_entity = PropertyOnboardingEntity(
            property_id="TEST_PROP_001",
            brand_id=1,
            template_filters={}
        )
        
        # Add created SKU codes
        onboarding_entity.created_entities["skus"] = ["SKU_001", "SKU_002", "SKU_003"]
        print(f"✅ Created onboarding entity with SKUs: {onboarding_entity.created_entities['skus']}")
        
        # Test 2: Mock service dependencies
        print("\n🔧 Test 2: Setting up mock services")
        
        # Create mock service parameters (POS, INVENTORY, REPORTING)
        mock_pos_param = Mock(spec=Param)
        mock_pos_param.id = 1
        mock_pos_param.value = "POS"
        
        mock_inventory_param = Mock(spec=Param)
        mock_inventory_param.id = 2
        mock_inventory_param.value = "INVENTORY"
        
        mock_reporting_param = Mock(spec=Param)
        mock_reporting_param.id = 3
        mock_reporting_param.value = "REPORTING"
        
        service_params = [mock_pos_param, mock_inventory_param, mock_reporting_param]
        print(f"✅ Created mock service parameters: {[p.value for p in service_params]}")
        
        # Create mock property SKUs
        mock_property_skus = []
        for i, sku_code in enumerate(["SKU_001", "SKU_002", "SKU_003"], 1):
            mock_sku = Mock(spec=Sku)
            mock_sku.code = sku_code
            
            mock_property_sku = Mock(spec=PropertySku)
            mock_property_sku.property_id = "TEST_PROP_001"
            mock_property_sku.sku_id = i
            mock_property_sku.sku = mock_sku
            
            mock_property_skus.append(mock_property_sku)
        
        print(f"✅ Created mock property SKUs: {[ps.sku.code for ps in mock_property_skus]}")
        
        # Test 3: Mock the service and its dependencies
        print("\n🏗️ Test 3: Creating mock PropertyOnboardingService")
        
        with patch('cataloging_service.domain.services.template.property_onboarding_service.service_provider') as mock_service_provider:
            # Mock meta_service
            mock_meta_service = Mock()
            mock_meta_service.sget_entity_params.return_value = service_params
            mock_service_provider.meta_service = mock_meta_service
            
            # Mock property_service
            mock_property_service = Mock()
            mock_property_service.get_property.return_value = Mock()
            mock_property_service.sget_sku_for_given_property_and_sku_codes.return_value = mock_property_skus
            mock_property_service.scheck_if_sku_exists_for_property_and_service.return_value = False
            mock_property_service._PropertyService__property_repository.persist_all.return_value = None
            mock_service_provider.property_service = mock_property_service
            
            # Create service instance with mocked dependencies
            mock_service = PropertyOnboardingService(
                department_template_service=Mock(),
                profit_center_template_service=Mock(),
                sku_template_service=Mock(),
                property_department_service=Mock(),
                property_profit_center_service=Mock(),
                property_sku_service=Mock(),
                transaction_default_mapping_service=Mock(),
                payment_method_service=Mock(),
                transaction_master_service=Mock()
            )
            
            print("✅ Created mock PropertyOnboardingService with dependencies")
            
            # Test 4: Call the _create_sku_activation_records method
            print("\n🚀 Test 4: Calling _create_sku_activation_records method")
            
            activation_record_ids = mock_service._create_sku_activation_records(onboarding_entity)
            
            print(f"✅ Method executed successfully")
            print(f"📊 Returned activation record IDs: {activation_record_ids}")
            
            # Test 5: Verify the results
            print("\n✅ Test 5: Verifying results")
            
            # Should have 9 activation records (3 SKUs × 3 services)
            expected_count = 9
            if len(activation_record_ids) == expected_count:
                print(f"✅ Correct number of activation records created: {len(activation_record_ids)}")
            else:
                print(f"❌ Expected {expected_count} activation records, got {len(activation_record_ids)}")
                return False
            
            # Verify the format of activation record IDs
            expected_format_samples = [
                "TEST_PROP_001-SKU_001-POS",
                "TEST_PROP_001-SKU_001-INVENTORY",
                "TEST_PROP_001-SKU_001-REPORTING"
            ]
            
            for expected_id in expected_format_samples:
                if expected_id in activation_record_ids:
                    print(f"✅ Found expected activation record ID: {expected_id}")
                else:
                    print(f"❌ Missing expected activation record ID: {expected_id}")
                    return False
            
            # Verify service calls
            print("\n🔍 Test 6: Verifying service method calls")
            
            # Verify meta_service.sget_entity_params was called
            mock_meta_service.sget_entity_params.assert_called_once_with('SkuActivation', 'service_id', True)
            print("✅ meta_service.sget_entity_params called correctly")
            
            # Verify property_service methods were called
            mock_property_service.get_property.assert_called_once_with("TEST_PROP_001")
            mock_property_service.sget_sku_for_given_property_and_sku_codes.assert_called_once()
            print("✅ property_service methods called correctly")
            
            # Verify persist_all was called
            mock_property_service._PropertyService__property_repository.persist_all.assert_called_once()
            print("✅ persist_all called to save activation records")
            
            print("\n🎉 All tests passed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling scenarios"""
    
    print("\n🛡️ Testing Error Handling Scenarios")
    print("=" * 60)
    
    try:
        from cataloging_service.domain.entities.properties.property_onboarding import PropertyOnboardingEntity
        from cataloging_service.domain.services.template.property_onboarding_service import PropertyOnboardingService
        
        # Test 1: No SKUs created
        print("\n📋 Test 1: No SKUs created scenario")
        onboarding_entity = PropertyOnboardingEntity(
            property_id="TEST_PROP_002",
            brand_id=1,
            template_filters={}
        )
        # Don't add any SKUs to created_entities
        
        with patch('cataloging_service.domain.services.template.property_onboarding_service.service_provider') as mock_service_provider:
            mock_meta_service = Mock()
            mock_service_provider.meta_service = mock_meta_service
            mock_service_provider.property_service = Mock()
            
            mock_service = PropertyOnboardingService(
                department_template_service=Mock(),
                profit_center_template_service=Mock(),
                sku_template_service=Mock(),
                property_department_service=Mock(),
                property_profit_center_service=Mock(),
                property_sku_service=Mock(),
                transaction_default_mapping_service=Mock(),
                payment_method_service=Mock(),
                transaction_master_service=Mock()
            )
            
            activation_record_ids = mock_service._create_sku_activation_records(onboarding_entity)
            
            if len(activation_record_ids) == 0 and len(onboarding_entity.warnings) > 0:
                print("✅ Correctly handled no SKUs scenario with warning")
            else:
                print("❌ Failed to handle no SKUs scenario correctly")
                return False
        
        print("\n🎉 Error handling tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 SKU Activation Records Implementation Test Suite")
    print("=" * 60)
    
    # Run main functionality test
    main_test_passed = test_sku_activation_records_implementation()
    
    # Run error handling test
    error_test_passed = test_error_handling()
    
    print("\n📊 Test Summary")
    print("=" * 60)
    print(f"Main functionality test: {'PASS' if main_test_passed else 'FAIL'}")
    print(f"Error handling test: {'PASS' if error_test_passed else 'FAIL'}")
    
    if main_test_passed and error_test_passed:
        print("\n🎉 All tests passed! The _create_sku_activation_records method is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please review the implementation.")
        sys.exit(1)
