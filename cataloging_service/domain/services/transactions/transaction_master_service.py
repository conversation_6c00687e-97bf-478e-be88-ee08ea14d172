from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity
from cataloging_service.infrastructure.repositories.transaction_master_repository import TransactionMasterRepository
from object_registry import register_instance


@register_instance(
    dependencies=[TransactionMasterRepository]
)
class TransactionMasterService:
    def __init__(self, transaction_master_repository):
        self.transaction_master_repository = transaction_master_repository

    def create_transaction(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        """Create transaction"""
        return self.transaction_master_repository.create(entity)


