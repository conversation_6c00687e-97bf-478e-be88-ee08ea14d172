"""
Transaction Default Mapping Domain Entity
"""

from datetime import datetime
from typing import Optional, Dict, Any

from pydantic import Field

from cataloging_service.domain.entities.base_entity import BaseDomainEntity

class TransactionMasterEntity(BaseDomainEntity):
    id: Optional[int] = Field(..., description="Unique ID")
    transaction_code: str = Field(..., description="Unique transaction code")
    name: str = Field(..., description="Transaction name")
    property_id: str = Field(..., description="Property ID")
    entity_type: str = Field(..., description="Entity type (FRANCHISER or HOTEL)")
    transaction_type: str = Field(..., description="Type of transaction")
    transaction_id: str = Field(..., description="Triggering transaction ID (sku_id, payment_id)")
    operational_unit_id: str = Field(..., description="Operational unit identifier")
    operational_unit_type: str = Field(..., description="Type of operational unit")
    source: str = Field(..., description="Source system or module")
    gl_code: Optional[str] = Field(None, description="General Ledger code")
    erp_id: Optional[str] = Field(None, description="ERP system identifier")
    is_merge: bool = Field(False, description="Whether this is a merge transaction")
    particulars: Optional[str] = Field(None, description="Transaction particulars/description")
    status: str = Field(..., description="Transaction status")
    transaction_details: Dict[str, Any] = Field(
        default_factory=dict, description="Transaction details JSON"
    )
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
