"""
Transaction Master Adapter
Converts between TransactionMasterEntity and TransactionMaster model.
"""

from datetime import datetime, timezone

from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity
from cataloging_service.models import TransactionMaster


class TransactionMasterAdapter:
    @staticmethod
    def to_entity(model: TransactionMaster) -> TransactionMasterEntity:
        return TransactionMasterEntity(
            id=model.id,
            entity_type=model.entity_type,
            transaction_type=model.transaction_type,
            property_id=model.property_id,
            name=model.name,
            transaction_id=model.transaction_id,
            transaction_code=model.transaction_code,
            operational_unit_id=model.operational_unit_id,
            operational_unit_type=model.operational_unit_type,
            source=model.source,
            gl_code=model.gl_code,
            erp_id=model.erp_id,
            is_merge=model.is_merge,
            particulars=model.particulars,
            status=model.status,
            transaction_details=model.transaction_details or {},
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_model(entity: TransactionMasterEntity) -> TransactionMaster:
        model = TransactionMaster()
        if entity.id:
            model.id = entity.id
        model.entity_type = entity.entity_type
        model.transaction_type = entity.transaction_type
        model.property_id = entity.property_id
        model.name = entity.name
        model.transaction_id = entity.transaction_id
        model.transaction_code = entity.transaction_code
        model.operational_unit_id = entity.operational_unit_id
        model.operational_unit_type = entity.operational_unit_type
        model.source = entity.source
        model.gl_code = entity.gl_code
        model.erp_id = entity.erp_id
        model.is_merge = entity.is_merge
        model.particulars = entity.particulars
        model.status = entity.status
        model.transaction_details = entity.transaction_details
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model

    @staticmethod
    def to_db_model(entity: TransactionMasterEntity) -> TransactionMasterEntity:
        return TransactionMasterAdapter.to_model(entity)

    @staticmethod
    def update_model_from_entity(
        model: TransactionMaster, entity: TransactionMasterEntity
    ) -> TransactionMaster:
        model.entity_type = entity.entity_type
        model.transaction_type = entity.transaction_type
        model.property_id = entity.property_id
        model.name = entity.name
        model.transaction_id = entity.transaction_id
        model.transaction_code = entity.transaction_code
        model.operational_unit_id = entity.operational_unit_id
        model.operational_unit_type = entity.operational_unit_type
        model.source = entity.source
        model.gl_code = entity.gl_code
        model.erp_id = entity.erp_id
        model.is_merge = entity.is_merge
        model.particulars = entity.particulars
        model.status = entity.status
        model.transaction_details = entity.transaction_details
        model.modified_at = datetime.now(timezone.utc)
        return model
