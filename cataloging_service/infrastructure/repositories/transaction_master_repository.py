"""
Transaction Default Mapping Repository
"""

from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import (
    BaseEntityRepository,
)
from cataloging_service.infrastructure.adapters.transaction_master_adapter import TransactionMasterAdapter
from cataloging_service.domain.entities.transactions.transaction_master import TransactionMasterEntity


@register_instance()
class TransactionMasterRepository(BaseEntityRepository):
    adaptor = TransactionMasterAdapter()

    def create(self, entity: TransactionMasterEntity) -> TransactionMasterEntity:
        model = self.adaptor.to_db_model(entity)
        self.session().add(model)
        self.session().commit()
        return self.adaptor.to_entity(model)

